<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- ========================================
         FLUENT DESIGN SPACING SYSTEM (8PT GRID)
         ======================================== -->

    <!-- Base Unit (8pt) -->
    <sys:Double x:Key="SpacingUnit">8</sys:Double>

    <!-- ========================================
         SPACING VALUES
         ======================================== -->

    <!-- Extra Small Spacing -->
    <sys:Double x:Key="SpacingXS">2</sys:Double>      <!-- 0.25 * 8pt -->
    <sys:Double x:Key="SpacingXXS">4</sys:Double>     <!-- 0.5 * 8pt -->

    <!-- Small Spacing -->
    <sys:Double x:Key="SpacingS">8</sys:Double>       <!-- 1 * 8pt -->
    <sys:Double x:Key="SpacingSM">12</sys:Double>     <!-- 1.5 * 8pt -->

    <!-- Medium Spacing -->
    <sys:Double x:Key="SpacingM">16</sys:Double>      <!-- 2 * 8pt -->
    <sys:Double x:Key="SpacingML">20</sys:Double>     <!-- 2.5 * 8pt -->

    <!-- Large Spacing -->
    <sys:Double x:Key="SpacingL">24</sys:Double>      <!-- 3 * 8pt -->
    <sys:Double x:Key="SpacingLX">32</sys:Double>     <!-- 4 * 8pt -->

    <!-- Extra Large Spacing -->
    <sys:Double x:Key="SpacingXL">40</sys:Double>     <!-- 5 * 8pt -->
    <sys:Double x:Key="SpacingXXL">48</sys:Double>    <!-- 6 * 8pt -->

    <!-- Huge Spacing -->
    <sys:Double x:Key="SpacingHuge">64</sys:Double>   <!-- 8 * 8pt -->
    <sys:Double x:Key="SpacingMassive">80</sys:Double> <!-- 10 * 8pt -->

    <!-- ========================================
         COMPONENT-SPECIFIC SPACING
         ======================================== -->

    <!-- Button Spacing -->
    <sys:Double x:Key="ButtonPaddingHorizontal">16</sys:Double>
    <sys:Double x:Key="ButtonPaddingVertical">8</sys:Double>
    <sys:Double x:Key="ButtonSpacing">8</sys:Double>
    <sys:Double x:Key="ButtonGroupSpacing">4</sys:Double>

    <!-- Input Control Spacing -->
    <sys:Double x:Key="InputPaddingHorizontal">12</sys:Double>
    <sys:Double x:Key="InputPaddingVertical">8</sys:Double>
    <sys:Double x:Key="InputSpacing">16</sys:Double>
    <sys:Double x:Key="InputLabelSpacing">4</sys:Double>

    <!-- Card Spacing -->
    <sys:Double x:Key="CardPadding">16</sys:Double>
    <sys:Double x:Key="CardSpacing">16</sys:Double>
    <sys:Double x:Key="CardHeaderSpacing">12</sys:Double>
    <sys:Double x:Key="CardContentSpacing">8</sys:Double>

    <!-- List Item Spacing -->
    <sys:Double x:Key="ListItemPaddingHorizontal">16</sys:Double>
    <sys:Double x:Key="ListItemPaddingVertical">12</sys:Double>
    <sys:Double x:Key="ListItemSpacing">4</sys:Double>

    <!-- Navigation Spacing -->
    <sys:Double x:Key="NavigationItemPaddingHorizontal">16</sys:Double>
    <sys:Double x:Key="NavigationItemPaddingVertical">12</sys:Double>
    <sys:Double x:Key="NavigationItemSpacing">4</sys:Double>
    <sys:Double x:Key="NavigationGroupSpacing">24</sys:Double>

    <!-- Dialog Spacing -->
    <sys:Double x:Key="DialogPadding">24</sys:Double>
    <sys:Double x:Key="DialogContentSpacing">16</sys:Double>
    <sys:Double x:Key="DialogButtonSpacing">8</sys:Double>

    <!-- Panel Spacing -->
    <sys:Double x:Key="PanelPadding">16</sys:Double>
    <sys:Double x:Key="PanelHeaderSpacing">12</sys:Double>
    <sys:Double x:Key="PanelContentSpacing">8</sys:Double>

    <!-- ========================================
         THICKNESS VALUES
         ======================================== -->

    <!-- Uniform Thickness -->
    <Thickness x:Key="ThicknessXS">2</Thickness>
    <Thickness x:Key="ThicknessXXS">4</Thickness>
    <Thickness x:Key="ThicknessS">8</Thickness>
    <Thickness x:Key="ThicknessSM">12</Thickness>
    <Thickness x:Key="ThicknessM">16</Thickness>
    <Thickness x:Key="ThicknessML">20</Thickness>
    <Thickness x:Key="ThicknessL">24</Thickness>
    <Thickness x:Key="ThicknessLX">32</Thickness>
    <Thickness x:Key="ThicknessXL">40</Thickness>
    <Thickness x:Key="ThicknessXXL">48</Thickness>
    <Thickness x:Key="ThicknessHuge">64</Thickness>

    <!-- Horizontal Thickness -->
    <Thickness x:Key="ThicknessHorizontalXS">2,0</Thickness>
    <Thickness x:Key="ThicknessHorizontalS">8,0</Thickness>
    <Thickness x:Key="ThicknessHorizontalM">16,0</Thickness>
    <Thickness x:Key="ThicknessHorizontalL">24,0</Thickness>
    <Thickness x:Key="ThicknessHorizontalXL">40,0</Thickness>

    <!-- Vertical Thickness -->
    <Thickness x:Key="ThicknessVerticalXS">0,2</Thickness>
    <Thickness x:Key="ThicknessVerticalS">0,8</Thickness>
    <Thickness x:Key="ThicknessVerticalM">0,16</Thickness>
    <Thickness x:Key="ThicknessVerticalL">0,24</Thickness>
    <Thickness x:Key="ThicknessVerticalXL">0,40</Thickness>

    <!-- Component-Specific Thickness -->
    <Thickness x:Key="ButtonPadding">16,8</Thickness>
    <Thickness x:Key="ButtonPaddingSmall">12,6</Thickness>
    <Thickness x:Key="ButtonPaddingLarge">20,12</Thickness>

    <Thickness x:Key="InputPadding">12,8</Thickness>
    <Thickness x:Key="InputPaddingSmall">8,6</Thickness>
    <Thickness x:Key="InputPaddingLarge">16,12</Thickness>

    <Thickness x:Key="CardPadding">16</Thickness>
    <Thickness x:Key="CardPaddingSmall">12</Thickness>
    <Thickness x:Key="CardPaddingLarge">24</Thickness>

    <Thickness x:Key="ListItemPadding">16,12</Thickness>
    <Thickness x:Key="ListItemPaddingCompact">12,8</Thickness>
    <Thickness x:Key="ListItemPaddingComfortable">20,16</Thickness>

    <Thickness x:Key="DialogPadding">24</Thickness>
    <Thickness x:Key="DialogContentPadding">24,16</Thickness>

    <Thickness x:Key="PanelPadding">16</Thickness>
    <Thickness x:Key="PanelHeaderPadding">16,12</Thickness>

    <!-- ========================================
         GRID LENGTH VALUES
         ======================================== -->

    <!-- Auto and Star -->
    <GridLength x:Key="GridLengthAuto">Auto</GridLength>
    <GridLength x:Key="GridLengthStar">*</GridLength>

    <!-- Fixed Sizes -->
    <GridLength x:Key="GridLengthXS">2</GridLength>
    <GridLength x:Key="GridLengthS">8</GridLength>
    <GridLength x:Key="GridLengthM">16</GridLength>
    <GridLength x:Key="GridLengthL">24</GridLength>
    <GridLength x:Key="GridLengthXL">40</GridLength>

    <!-- Common Column Widths -->
    <GridLength x:Key="GridColumnNarrow">120</GridLength>
    <GridLength x:Key="GridColumnMedium">200</GridLength>
    <GridLength x:Key="GridColumnWide">300</GridLength>

    <!-- Common Row Heights -->
    <GridLength x:Key="GridRowSmall">32</GridLength>
    <GridLength x:Key="GridRowMedium">48</GridLength>
    <GridLength x:Key="GridRowLarge">64</GridLength>

    <!-- ========================================
         LAYOUT MARGINS
         ======================================== -->

    <!-- Page Margins -->
    <Thickness x:Key="PageMargin">24</Thickness>
    <Thickness x:Key="PageMarginSmall">16</Thickness>
    <Thickness x:Key="PageMarginLarge">32</Thickness>

    <!-- Section Margins -->
    <Thickness x:Key="SectionMargin">0,0,0,24</Thickness>
    <Thickness x:Key="SectionMarginSmall">0,0,0,16</Thickness>
    <Thickness x:Key="SectionMarginLarge">0,0,0,32</Thickness>

    <!-- Content Margins -->
    <Thickness x:Key="ContentMargin">0,0,0,16</Thickness>
    <Thickness x:Key="ContentMarginSmall">0,0,0,8</Thickness>
    <Thickness x:Key="ContentMarginLarge">0,0,0,24</Thickness>

    <!-- Header Margins -->
    <Thickness x:Key="HeaderMargin">0,0,0,12</Thickness>
    <Thickness x:Key="SubHeaderMargin">0,0,0,8</Thickness>

    <!-- ========================================
         RESPONSIVE SPACING
         ======================================== -->

    <!-- Mobile Spacing (smaller screens) -->
    <sys:Double x:Key="MobileSpacingS">4</sys:Double>
    <sys:Double x:Key="MobileSpacingM">8</sys:Double>
    <sys:Double x:Key="MobileSpacingL">16</sys:Double>
    <sys:Double x:Key="MobileSpacingXL">24</sys:Double>

    <!-- Desktop Spacing (larger screens) -->
    <sys:Double x:Key="DesktopSpacingS">8</sys:Double>
    <sys:Double x:Key="DesktopSpacingM">16</sys:Double>
    <sys:Double x:Key="DesktopSpacingL">24</sys:Double>
    <sys:Double x:Key="DesktopSpacingXL">40</sys:Double>

</ResourceDictionary>
