{"Version": 1, "WorkspaceRootPath": "D:\\08 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\views\\designsystemdemo.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\designsystemdemo.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\themes\\spacing\\spacing.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\themes\\spacing\\spacing.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\themes\\dark.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\themes\\dark.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\themes\\light.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\themes\\light.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\services\\themes\\thememanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\themes\\thememanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "Spacing.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Spacing\\Spacing.xaml", "RelativeDocumentMoniker": "AirMonitor\\Themes\\Spacing\\Spacing.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Spacing\\Spacing.xaml", "RelativeToolTip": "AirMonitor\\Themes\\Spacing\\Spacing.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:53:58.914Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "DesignSystemDemo.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Views\\DesignSystemDemo.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\DesignSystemDemo.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Views\\DesignSystemDemo.xaml", "RelativeToolTip": "AirMonitor\\Views\\DesignSystemDemo.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:53:50.017Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\appsettings.json", "RelativeDocumentMoniker": "AirMonitor\\appsettings.json", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\appsettings.json", "RelativeToolTip": "AirMonitor\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-19T07:52:04.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\App.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\App.xaml.cs", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\App.xaml.cs", "RelativeToolTip": "AirMonitor\\App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T07:51:36.197Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "App.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\App.xaml", "RelativeDocumentMoniker": "AirMonitor\\App.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\App.xaml", "RelativeToolTip": "AirMonitor\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:50:55.213Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Dark.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Dark.xaml", "RelativeDocumentMoniker": "AirMonitor\\Themes\\Dark.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Dark.xaml", "RelativeToolTip": "AirMonitor\\Themes\\Dark.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:50:38.079Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Light.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Light.xaml", "RelativeDocumentMoniker": "AirMonitor\\Themes\\Light.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Light.xaml", "RelativeToolTip": "AirMonitor\\Themes\\Light.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T07:50:04.055Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ThemeManager.cs", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Services\\Themes\\ThemeManager.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\Themes\\ThemeManager.cs", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Services\\Themes\\ThemeManager.cs", "RelativeToolTip": "AirMonitor\\Services\\Themes\\ThemeManager.cs", "ViewState": "AgIAABcAAAAAAAAAAAAIwB0AAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T07:49:14.48Z", "EditorCaption": ""}]}]}]}