using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AirMonitor.Models;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainWindowViewModel : ViewModelBase
{
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly AppSettings _appSettings;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志服务</param>
    /// <param name="appSettings">应用程序设置</param>
    public MainWindowViewModel(ILogger<MainWindowViewModel> logger, IOptions<AppSettings> appSettings)
    {
        _logger = logger;
        _appSettings = appSettings.Value;
        
        Title = _appSettings.Application.Name;
        StatusMessage = "应用程序已启动";
        
        _logger.LogInformation("MainWindowViewModel 已初始化");
    }

    /// <summary>
    /// 示例命令
    /// </summary>
    [RelayCommand]
    private void ShowMessage()
    {
        _logger.LogInformation("ShowMessage 命令被执行");
        StatusMessage = $"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
    }

    /// <summary>
    /// 退出应用程序命令
    /// </summary>
    [RelayCommand]
    private void ExitApplication()
    {
        _logger.LogInformation("ExitApplication 命令被执行");
        System.Windows.Application.Current.Shutdown();
    }
}
