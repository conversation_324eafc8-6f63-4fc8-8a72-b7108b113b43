﻿<Application x:Class="AirMonitor.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:AirMonitor"
             StartupUri="MainWindow.xaml">
    <Application.Resources>

        <!-- ========================================
             FLUENT DESIGN SYSTEM RESOURCES
             ======================================== -->

        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>

                <!-- 默认加载亮色主题 -->
                <!-- 主题将通过ThemeManager动态管理 -->
                <ResourceDictionary Source="Themes/Light.xaml"/>

            </ResourceDictionary.MergedDictionaries>

            <!-- ========================================
                 APPLICATION-WIDE STYLES
                 ======================================== -->

            <!-- 默认窗口样式 -->
            <Style TargetType="Window">
                <Setter Property="Background" Value="{DynamicResource WindowBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
                <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
                <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
                <Setter Property="TextOptions.TextFormattingMode" Value="Ideal"/>
                <Setter Property="TextOptions.TextRenderingMode" Value="Auto"/>
            </Style>

            <!-- 默认用户控件样式 -->
            <Style TargetType="UserControl">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
                <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
                <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
            </Style>

            <!-- 默认页面样式 -->
            <Style TargetType="Page">
                <Setter Property="Background" Value="{DynamicResource BackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
                <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
                <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
                <Setter Property="UseLayoutRounding" Value="True"/>
            </Style>

        </ResourceDictionary>

    </Application.Resources>
</Application>
