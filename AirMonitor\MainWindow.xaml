﻿<Window x:Class="AirMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AirMonitor"
        xmlns:vm="clr-namespace:AirMonitor.ViewModels"
        mc:Ignorable="d"
        Title="{Binding Title}" Height="450" Width="800"
        WindowStartupLocation="CenterScreen">



    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="退出(_X)" Command="{Binding ExitApplicationCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding ShowMessageCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0"
                       Text="欢迎使用 AirMonitor 应用程序框架"
                       FontSize="24"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,20"/>

            <StackPanel Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                <Button Content="显示消息"
                        Command="{Binding ShowMessageCommand}"
                        Width="120"
                        Height="35"
                        Margin="5"/>
                <Button Content="退出应用程序"
                        Command="{Binding ExitApplicationCommand}"
                        Width="120"
                        Height="35"
                        Margin="5"/>
            </StackPanel>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
